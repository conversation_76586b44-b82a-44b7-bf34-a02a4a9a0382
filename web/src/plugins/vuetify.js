import Vue from 'vue';
import Vuetify from 'vuetify/lib';
import OpenTofuIcon from '@/components/OpenTofuIcon.vue';
import PulumiIcon from '@/components/PulumiIcon.vue';

Vue.use(Vuetify);

export default new Vuetify({
  theme: {
    themes: {
      light: {
        primary: '#FF6B35',
        secondary: '#FF8E53',
        accent: '#FFA726',
        error: '#FF5252',
        info: '#2196F3',
        success: '#4CAF50',
        warning: '#FFC107',
        background: '#FFF5F0',
        surface: '#FFFFFF',
        'orange-gradient-start': '#FF6B35',
        'orange-gradient-end': '#FFA726',
      },
      dark: {
        primary: '#FF6B35',
        secondary: '#FF8E53',
        accent: '#FFA726',
        error: '#FF5252',
        info: '#2196F3',
        success: '#4CAF50',
        warning: '#FFC107',
        background: '#1A1A1A',
        surface: '#2D2D2D',
        'orange-gradient-start': '#FF6B35',
        'orange-gradient-end': '#FFA726',
      },
    },
  },
  icons: {
    values: {
      tofu: {
        component: OpenTofuIcon,
      },
      pulumi: {
        component: PulumiIcon,
      },
    },
  },
});
