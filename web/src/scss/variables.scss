$material-dark: (
        'background': #1E1E1E,
        'app-bar': transparent,
        'toolbar': transparent,
);

$material-light: (
        'app-bar': transparent,
        'toolbar': transparent,
);

$tabs-bar-background-color: 'toolbar';

// Orange theme variables
$orange-primary: #FF6B35;
$orange-secondary: #FF8E53;
$orange-accent: #FFA726;
$orange-light: #FFF5F0;
$orange-gradient-start: #FF6B35;
$orange-gradient-end: #FFA726;

:root {
  --breakpoint-sm: 600px;
  --breakpoint-md: 960px;
  --breakpoint-lg: 1264px;
  --breakpoint-xl: 1904px;
  --nav-drawer-width: 260px;

  // Orange theme CSS variables
  --orange-primary: #{$orange-primary};
  --orange-secondary: #{$orange-secondary};
  --orange-accent: #{$orange-accent};
  --orange-light: #{$orange-light};
  --orange-gradient: linear-gradient(135deg, #{$orange-gradient-start} 0%, #{$orange-gradient-end} 100%);
  --orange-gradient-light: linear-gradient(135deg, rgba(255, 107, 53, 0.1) 0%, rgba(255, 167, 38, 0.1) 100%);
}