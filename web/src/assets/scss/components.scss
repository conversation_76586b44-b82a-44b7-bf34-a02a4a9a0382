.FieldTable {
  .v-data-table__wrapper {
    padding-left: 0 !important;
    padding-right: 0 !important;
    td {
      border-bottom: 0 !important;
    }
    td:last-child {
      padding-right: 15px !important;
    }
    td:first-child {
      padding-left: 5px !important;
    }
  }
}

.vue-codemirror {
  border-radius: 6px !important;
}

.CodeMirror {
  border-radius: 5px !important;
}

// Orange theme for cards and components
.v-card {
  border-radius: 12px !important;
  box-shadow: 0 4px 20px rgba(255, 107, 53, 0.1) !important;

  &.orange-card {
    background: linear-gradient(135deg, rgba(255, 107, 53, 0.05) 0%, rgba(255, 167, 38, 0.05) 100%);
    border: 1px solid rgba(255, 107, 53, 0.1);
  }
}

// Orange theme for buttons
.v-btn {
  &.v-btn--contained.theme--light.primary {
    background: linear-gradient(45deg, #FF6B35, #FFA726) !important;
    border: none !important;
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3) !important;

    &:hover {
      box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4) !important;
    }
  }
}

// Orange theme for data tables
.v-data-table {
  .v-data-table__wrapper > table > thead > tr:last-child > th {
    background: linear-gradient(90deg, rgba(255, 107, 53, 0.1) 0%, rgba(255, 167, 38, 0.1) 100%);
    color: #FF6B35 !important;
    font-weight: 600;
  }

  .v-data-table__wrapper > table > tbody > tr:hover {
    background: rgba(255, 107, 53, 0.05) !important;
  }
}

// Orange theme for text fields
.v-text-field {
  &.v-text-field--outlined {
    .v-input__control .v-input__slot {
      border-color: rgba(255, 107, 53, 0.3) !important;
    }

    &.v-input--is-focused .v-input__control .v-input__slot {
      border-color: #FF6B35 !important;
      border-width: 2px !important;
    }
  }
}

// Orange theme for progress indicators
.v-progress-circular {
  &.primary--text {
    color: #FF6B35 !important;
  }
}

// Orange theme for alerts
.v-alert {
  &.primary {
    background: linear-gradient(45deg, rgba(255, 107, 53, 0.1), rgba(255, 167, 38, 0.1)) !important;
    border-left: 4px solid #FF6B35 !important;
    color: #FF6B35 !important;
  }
}