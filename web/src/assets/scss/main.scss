$mdi-font-path: "~@mdi/font/fonts";
@import '~@mdi/font/scss/materialdesignicons';

@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 100;
  font-display: swap;
  src: url("../fonts/Roboto-Thin.ttf") format('truetype');
}

@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 100;
  font-display: swap;
  src: url("../fonts/Roboto-Thin.ttf") format('truetype');
}

@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../fonts/Roboto-Light.ttf") format('truetype');
}

@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../fonts/Roboto-Regular.ttf") format('truetype');
}

@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../fonts/Roboto-Medium.ttf") format('truetype');
}

@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../fonts/Roboto-Bold.ttf") format('truetype');
}

@font-face {
  font-family: "Roboto";
  font-style: normal;
  font-weight: 900;
  font-display: swap;
  src: url("../fonts/Roboto-Black.ttf") format('truetype');
}

@import '~vuetify/src/styles/main';

// Orange gradient background
.orange-tech-background {
  background: linear-gradient(135deg, #FF6B35 0%, #FFA726 100%);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
      radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
    pointer-events: none;
  }

  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
      linear-gradient(45deg, transparent 40%, rgba(255, 255, 255, 0.03) 50%, transparent 60%),
      linear-gradient(-45deg, transparent 40%, rgba(255, 255, 255, 0.03) 50%, transparent 60%);
    background-size: 100px 100px;
    animation: tech-pattern 20s linear infinite;
    pointer-events: none;
  }
}

@keyframes tech-pattern {
  0% { transform: translateX(-100px) translateY(-100px); }
  100% { transform: translateX(100px) translateY(100px); }
}

// Tech circles animation
.tech-circles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  pointer-events: none;

  &::before,
  &::after {
    content: '';
    position: absolute;
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
  }

  &::before {
    width: 300px;
    height: 300px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
  }

  &::after {
    width: 200px;
    height: 200px;
    bottom: 20%;
    right: 15%;
    animation-delay: 3s;
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) scale(1); opacity: 0.3; }
  50% { transform: translateY(-20px) scale(1.05); opacity: 0.6; }
}

.v-application.theme--dark .v-overlay__scrim {
  background: #878787 !important;
}

.v-bottom-sheet > .v-sheet.theme--dark {
  background: #212121;
}

.opacity1 {
  opacity: 1 !important;
}

// Glass morphism effect
.glass-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

// Orange button styles
.v-btn.orange-gradient {
  background: linear-gradient(45deg, #FF6B35, #FFA726) !important;
  color: white !important;
  border: none !important;
  box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3) !important;
  transition: all 0.3s ease !important;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4) !important;
  }
}

@import "components";