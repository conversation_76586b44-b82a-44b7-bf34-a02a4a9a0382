<template>
  <div class="tech-background">
    <!-- Animated tech circles -->
    <div class="tech-circles">
      <div class="circle circle-1"></div>
      <div class="circle circle-2"></div>
      <div class="circle circle-3"></div>
    </div>
    
    <!-- Floating particles -->
    <div class="particles">
      <div class="particle" v-for="n in 20" :key="n" :style="getParticleStyle(n)"></div>
    </div>
    
    <!-- Grid pattern -->
    <div class="grid-pattern"></div>
    
    <!-- Content slot -->
    <div class="content">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TechBackground',
  methods: {
    getParticleStyle(index) {
      const size = Math.random() * 4 + 2;
      const left = Math.random() * 100;
      const animationDelay = Math.random() * 10;
      const animationDuration = Math.random() * 10 + 10;
      
      return {
        width: `${size}px`,
        height: `${size}px`,
        left: `${left}%`,
        animationDelay: `${animationDelay}s`,
        animationDuration: `${animationDuration}s`,
      };
    },
  },
};
</script>

<style lang="scss" scoped>
.tech-background {
  position: relative;
  min-height: 100vh;
  background: linear-gradient(135deg, #FF6B35 0%, #FFA726 100%);
  overflow: hidden;
}

.tech-circles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  
  .circle {
    position: absolute;
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: float 8s ease-in-out infinite;
    
    &.circle-1 {
      width: 300px;
      height: 300px;
      top: 10%;
      left: 10%;
      animation-delay: 0s;
    }
    
    &.circle-2 {
      width: 200px;
      height: 200px;
      top: 60%;
      right: 20%;
      animation-delay: 2s;
    }
    
    &.circle-3 {
      width: 150px;
      height: 150px;
      bottom: 20%;
      left: 60%;
      animation-delay: 4s;
    }
  }
}

.particles {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  
  .particle {
    position: absolute;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    animation: particle-float linear infinite;
  }
}

.grid-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: grid-move 20s linear infinite;
  pointer-events: none;
}

.content {
  position: relative;
  z-index: 1;
}

@keyframes float {
  0%, 100% { 
    transform: translateY(0px) scale(1); 
    opacity: 0.3; 
  }
  50% { 
    transform: translateY(-30px) scale(1.1); 
    opacity: 0.6; 
  }
}

@keyframes particle-float {
  0% {
    transform: translateY(100vh) translateX(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) translateX(50px);
    opacity: 0;
  }
}

@keyframes grid-move {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}
</style>
