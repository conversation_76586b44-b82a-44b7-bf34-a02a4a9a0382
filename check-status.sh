#!/bin/bash

# Semaphore 开发环境状态检查脚本
# 使用方法: ./check-status.sh

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[✓]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[!]${NC} $1"
}

log_error() {
    echo -e "${RED}[✗]${NC} $1"
}

# 检查端口状态
check_port() {
    local port=$1
    local service_name=$2
    local url=$3
    
    if lsof -i :$port &> /dev/null; then
        log_success "$service_name 正在运行 (端口 $port)"
        
        # 如果提供了URL，测试HTTP连接
        if [ ! -z "$url" ]; then
            if curl -s "$url" > /dev/null 2>&1; then
                log_success "$service_name HTTP 响应正常"
            else
                log_warning "$service_name 端口开放但HTTP无响应"
            fi
        fi
        return 0
    else
        log_error "$service_name 未运行 (端口 $port)"
        return 1
    fi
}

# 检查文件状态
check_file() {
    local file_path=$1
    local description=$2
    
    if [ -f "$file_path" ]; then
        local size=$(ls -lh "$file_path" | awk '{print $5}')
        log_success "$description 存在 ($size)"
        return 0
    else
        log_error "$description 不存在"
        return 1
    fi
}

# 检查目录状态
check_directory() {
    local dir_path=$1
    local description=$2
    
    if [ -d "$dir_path" ]; then
        local count=$(find "$dir_path" -type f | wc -l)
        log_success "$description 存在 ($count 个文件)"
        return 0
    else
        log_error "$description 不存在"
        return 1
    fi
}

# 检查进程状态
check_process() {
    local process_pattern=$1
    local description=$2
    
    local pids=$(pgrep -f "$process_pattern" 2>/dev/null || true)
    
    if [ ! -z "$pids" ]; then
        local count=$(echo "$pids" | wc -l)
        log_success "$description 运行中 ($count 个进程)"
        return 0
    else
        log_error "$description 未运行"
        return 1
    fi
}

# 主函数
main() {
    echo "==============================="
    echo "  Semaphore 开发环境状态检查"
    echo "==============================="
    echo ""
    
    # 检查基础文件
    log_info "检查基础文件..."
    check_file "bin/semaphore" "后端二进制文件"
    check_file "config.json" "配置文件"
    check_file "database.boltdb" "数据库文件"
    check_directory "web" "前端项目目录"
    check_directory "web/node_modules" "前端依赖"
    echo ""
    
    # 检查服务状态
    log_info "检查服务状态..."
    backend_running=$(check_port 3000 "后端 API 服务器" "http://localhost:3000/api/ping" && echo "true" || echo "false")
    frontend_running=$(check_port 8080 "前端开发服务器" "http://localhost:8080" && echo "true" || echo "false")
    echo ""
    
    # 检查进程
    log_info "检查进程状态..."
    check_process "semaphore server" "Semaphore 后端进程"
    check_process "npm run serve" "前端开发进程"
    echo ""
    
    # 检查日志文件
    log_info "检查日志文件..."
    if [ -f "backend.log" ]; then
        local backend_log_size=$(ls -lh backend.log | awk '{print $5}')
        log_info "后端日志: backend.log ($backend_log_size)"
        echo "  最后几行:"
        tail -3 backend.log | sed 's/^/    /'
    else
        log_info "后端日志文件不存在"
    fi
    
    if [ -f "frontend.log" ]; then
        local frontend_log_size=$(ls -lh frontend.log | awk '{print $5}')
        log_info "前端日志: frontend.log ($frontend_log_size)"
        echo "  最后几行:"
        tail -3 frontend.log | sed 's/^/    /'
    else
        log_info "前端日志文件不存在"
    fi
    echo ""
    
    # 显示访问链接
    log_info "访问链接..."
    if [ "$backend_running" = "true" ]; then
        echo "  🔧 后端 API: http://localhost:3000"
        echo "  📋 API 文档: http://localhost:3000/api/ping"
    fi
    
    if [ "$frontend_running" = "true" ]; then
        echo "  🌐 前端开发: http://localhost:8080"
    fi
    
    if [ "$backend_running" = "true" ] && [ "$frontend_running" = "false" ]; then
        echo "  🌐 生产模式: http://localhost:3000 (需要构建前端)"
    fi
    echo ""
    
    # 显示快速命令
    log_info "快速命令..."
    echo "  启动开发环境: ./start-dev.sh"
    echo "  停止开发环境: ./stop-dev.sh"
    echo "  只启动后端: ./start-dev.sh --backend-only"
    echo "  只启动前端: ./start-dev.sh --frontend-only"
    echo "  查看后端日志: tail -f backend.log"
    echo "  查看前端日志: tail -f frontend.log"
    echo ""
    
    # 总结状态
    if [ "$backend_running" = "true" ] && [ "$frontend_running" = "true" ]; then
        log_success "开发环境完全运行中！"
    elif [ "$backend_running" = "true" ]; then
        log_warning "只有后端在运行"
    elif [ "$frontend_running" = "true" ]; then
        log_warning "只有前端在运行"
    else
        log_error "开发环境未运行"
    fi
    
    echo "==============================="
}

# 运行主函数
main
